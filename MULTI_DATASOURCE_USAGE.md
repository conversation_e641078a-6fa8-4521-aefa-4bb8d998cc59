# 多数据源使用指南

## 简化的多数据源实现

这是一个超级简化的多数据源实现，使用注解来标识不同的数据源。

### 核心组件

1. **注解**：
   - `@Primary` - 主数据源标识
   - `@Secondary` - 辅助数据源标识

2. **Repository**：
   - `AssetRepository` - 主数据源操作
   - `SecondaryAssetRepository` - 辅助数据源操作

3. **Service**：
   - 直接注入两个 Repository
   - 明确选择使用哪个数据源

### 使用示例

```java
@ApplicationScoped
public class AssetService {
    
    @Inject @Primary
    AssetRepository primaryRepo;
    
    @Inject @Secondary  
    SecondaryAssetRepository secondaryRepo;
    
    // 保存到主数据源
    public Asset saveToPrimary(Asset asset) {
        return primaryRepo.save(asset);
    }
    
    // 保存到辅助数据源
    public Asset saveToSecondary(Asset asset) {
        return secondaryRepo.save(asset);
    }
    
    // 跨数据源操作：归档
    public void archiveAsset(Long id) {
        Asset asset = primaryRepo.findById(id).orElseThrow();
        asset.setStatus("ARCHIVED");
        secondaryRepo.save(asset);  // 保存到辅助库
        primaryRepo.deleteById(id); // 从主库删除
    }
}
```

### API 端点

- `GET /api/assets` - 获取主库所有资产
- `GET /api/assets/archived` - 获取辅助库归档资产
- `POST /api/assets/{id}/archive` - 将资产从主库移动到辅助库
- `GET /api/assets/stats` - 获取两个数据库的统计信息

### 配置

在 `application.yml` 中配置两个数据源：

```yaml
quarkus:
  datasource:
    db-kind: postgresql
    username: postgres
    password: postgres
    jdbc:
      url: *******************************************
    
    secondary:
      db-kind: mysql
      username: mysql
      password: mysql
      jdbc:
        url: ************************************************

  hibernate-orm:
    database:
      generation: update
    
    secondary:
      database:
        generation: update
```

### 优势

1. **简单直观** - 不需要复杂的拦截器或路由逻辑
2. **明确控制** - 在代码中明确选择使用哪个数据源
3. **易于测试** - 可以独立测试每个数据源
4. **易于维护** - 代码结构清晰，容易理解和修改
