package com.example.service;

import com.example.annotation.Primary;
import com.example.dto.AssetDTO;
import com.example.entity.Asset;
import com.example.repository.AssetRepository;
import com.example.service.MultiDataSourceAssetService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@ApplicationScoped
public class AssetService {

    private static final Logger logger = LoggerFactory.getLogger(AssetService.class);

    @Inject
    AssetRepository assetRepository;

    @Inject
    MultiDataSourceAssetService multiDataSourceAssetService;

    /**
     * Create a new asset
     */
    @Primary
    @Transactional
    public AssetDTO createAsset(AssetDTO assetDTO) {
        logger.info("Creating new asset: {}", assetDTO.name());

        Asset asset = convertToEntity(assetDTO);
        asset.setCreatedAt(LocalDateTime.now());

        Asset savedAsset = assetRepository.save(asset);
        return convertToDTO(savedAsset);
    }

    /**
     * Update an existing asset
     */
    @Primary
    @Transactional
    public AssetDTO updateAsset(AssetDTO assetDTO) {
        logger.info("Updating asset: {}", assetDTO.id());

        Asset asset = convertToEntity(assetDTO);
        Asset updatedAsset = assetRepository.update(asset);
        return convertToDTO(updatedAsset);
    }

    /**
     * Find asset by ID
     */
    @Primary
    public Optional<AssetDTO> findById(Long id) {
        logger.debug("Finding asset by id: {}", id);
        return assetRepository.findById(id)
                .map(this::convertToDTO);
    }

    /**
     * Find all assets
     */
    @Primary
    public List<AssetDTO> findAll() {
        logger.debug("Finding all assets");
        return assetRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Find assets by type
     */
    @Primary
    public List<AssetDTO> findByType(String type) {
        logger.debug("Finding assets by type: {}", type);
        return assetRepository.findByType(type).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Find assets by status
     */
    @Primary
    public List<AssetDTO> findByStatus(String status) {
        logger.debug("Finding assets by status: {}", status);
        return assetRepository.findByStatus(status).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * Delete asset by ID
     */
    @Primary
    @Transactional
    public void deleteById(Long id) {
        logger.info("Deleting asset by id: {}", id);
        assetRepository.deleteById(id);
    }

    /**
     * Get asset statistics
     */
    public MultiDataSourceAssetService.AssetStatistics getStatistics() {
        return multiDataSourceAssetService.getAssetStatistics();
    }

    /**
     * Archive old assets
     */
    @Transactional
    public void archiveOldAssets(int daysOld) {
        multiDataSourceAssetService.archiveOldAssets(daysOld);
    }

    /**
     * Convert Entity to DTO
     */
    private AssetDTO convertToDTO(Asset asset) {
        return new AssetDTO(
                asset.getId(),
                asset.getName(),
                asset.getType(),
                asset.getValue(),
                asset.getStatus(),
                asset.getCreatedAt(),
                asset.getDescription()
        );
    }

    /**
     * Convert DTO to Entity
     */
    private Asset convertToEntity(AssetDTO assetDTO) {
        Asset asset = new Asset();
        asset.setId(assetDTO.id());
        asset.setName(assetDTO.name());
        asset.setType(assetDTO.type());
        asset.setValue(assetDTO.value());
        asset.setStatus(assetDTO.status());
        asset.setCreatedAt(assetDTO.createdAt());
        asset.setDescription(assetDTO.description());
        return asset;
    }
}