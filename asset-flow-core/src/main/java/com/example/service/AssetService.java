package com.example.service;

import com.example.annotation.Primary;
import com.example.annotation.Secondary;
import com.example.dto.AssetDTO;
import com.example.entity.Asset;
import com.example.repository.AssetRepository;
import com.example.repository.SecondaryAssetRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 简化的 Asset Service - 直接使用两个 Repository
 */
@ApplicationScoped
public class AssetService {

    private static final Logger logger = LoggerFactory.getLogger(AssetService.class);

    @Inject
    @Primary
    AssetRepository primaryRepo;

    @Inject
    @Secondary
    SecondaryAssetRepository secondaryRepo;

    // === 主数据源操作 ===

    @Transactional
    public AssetDTO createAsset(AssetDTO dto) {
        logger.info("Creating asset in primary DB: {}", dto.name());
        Asset asset = toEntity(dto);
        asset.setCreatedAt(LocalDateTime.now());
        Asset saved = primaryRepo.save(asset);
        return toDTO(saved);
    }

    public Optional<AssetDTO> findById(Long id) {
        return primaryRepo.findById(id).map(this::toDTO);
    }

    public List<AssetDTO> findAll() {
        return primaryRepo.findAll().stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    public List<AssetDTO> findByType(String type) {
        return primaryRepo.findByType(type).stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    @Transactional
    public void deleteById(Long id) {
        primaryRepo.deleteById(id);
    }

    // === 辅助数据源操作 ===

    @Transactional
    public AssetDTO archiveAsset(AssetDTO dto) {
        logger.info("Archiving asset to secondary DB: {}", dto.name());
        Asset asset = toEntity(dto);
        asset.setStatus("ARCHIVED");
        Asset saved = secondaryRepo.save(asset);
        return toDTO(saved);
    }

    public List<AssetDTO> findArchivedAssets() {
        return secondaryRepo.findArchivedAssets().stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    public Optional<AssetDTO> findArchivedById(Long id) {
        return secondaryRepo.findById(id).map(this::toDTO);
    }

    // === 跨数据源操作 ===

    @Transactional
    public void moveToArchive(Long id) {
        logger.info("Moving asset {} from primary to secondary DB", id);

        // 从主库查找
        Optional<Asset> assetOpt = primaryRepo.findById(id);
        if (assetOpt.isPresent()) {
            Asset asset = assetOpt.get();
            asset.setStatus("ARCHIVED");

            // 保存到辅助库
            secondaryRepo.save(asset);

            // 从主库删除
            primaryRepo.deleteById(id);

            logger.info("Successfully moved asset {} to archive", id);
        }
    }

    public AssetStats getStats() {
        long primaryCount = primaryRepo.count();
        long secondaryCount = secondaryRepo.count();
        return new AssetStats(primaryCount, secondaryCount);
    }

    // === 工具方法 ===

    private AssetDTO toDTO(Asset asset) {
        return new AssetDTO(
                asset.getId(),
                asset.getName(),
                asset.getType(),
                asset.getValue(),
                asset.getStatus(),
                asset.getCreatedAt(),
                asset.getDescription()
        );
    }

    private Asset toEntity(AssetDTO dto) {
        Asset asset = new Asset();
        asset.setId(dto.id());
        asset.setName(dto.name());
        asset.setType(dto.type());
        asset.setValue(dto.value());
        asset.setStatus(dto.status());
        asset.setCreatedAt(dto.createdAt());
        asset.setDescription(dto.description());
        return asset;
    }

    public record AssetStats(long primaryCount, long secondaryCount) {}
}