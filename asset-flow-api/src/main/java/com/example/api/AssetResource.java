package com.example.api;

import com.example.dto.AssetDTO;
import com.example.service.AssetService;
import io.smallrye.common.annotation.NonBlocking;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

@Path("/api/assets")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Asset Management", description = "Operations for managing assets")
public class AssetResource {

    @Inject
    AssetService assetService;

    @GET
    @Operation(summary = "Get all assets from primary database")
    public List<AssetDTO> getAllAssets() {
        return assetService.findAll();
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "Get asset by ID from primary database")
    public Response getAssetById(@PathParam("id") Long id) {
        return assetService.findById(id)
                .map(asset -> Response.ok(asset).build())
                .orElse(Response.status(Status.NOT_FOUND).build());
    }

    @POST
    @Operation(summary = "Create new asset in primary database")
    public Response createAsset(@Valid AssetDTO assetDTO) {
        AssetDTO created = assetService.createAsset(assetDTO);
        return Response.status(Status.CREATED).entity(created).build();
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "Delete asset from primary database")
    public Response deleteAsset(@PathParam("id") Long id) {
        assetService.deleteById(id);
        return Response.noContent().build();
    }

    @GET
    @Path("/type/{type}")
    @Operation(summary = "Get assets by type from primary database")
    public List<AssetDTO> getAssetsByType(@PathParam("type") String type) {
        return assetService.findByType(type);
    }

    @GET
    @Path("/archived")
    @Operation(summary = "Get all archived assets from secondary database")
    public List<AssetDTO> getArchivedAssets() {
        return assetService.findArchivedAssets();
    }

    @POST
    @Path("/{id}/archive")
    @Operation(summary = "Move asset from primary to secondary database")
    public Response archiveAsset(@PathParam("id") Long id) {
        assetService.moveToArchive(id);
        return Response.ok().build();
    }

    @GET
    @Path("/stats")
    @Operation(summary = "Get statistics from both databases")
    public AssetService.AssetStats getStats() {
        return assetService.getStats();
    }
}