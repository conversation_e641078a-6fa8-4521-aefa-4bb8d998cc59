quarkus:
  application:
    name: asset-flow
  http:
    port: 8080

  # Primary Datasource (PostgreSQL)
  datasource:
    db-kind: postgresql
    username: postgres
    password: postgres
    jdbc:
      url: *******************************************
      max-size: 16
      min-size: 2
      initial-size: 2
      acquisition-timeout: 30s
      leak-detection-interval: 10m

    # Secondary Datasource (MySQL)
    secondary:
      db-kind: mysql
      username: mysql
      password: mysql
      jdbc:
        url: ******************************************************************************************
        max-size: 16
        min-size: 2
        initial-size: 2
        acquisition-timeout: 30s
        leak-detection-interval: 10m

  # Hibernate ORM Configuration for Primary DB
  hibernate-orm:
    database:
      generation: update
    log:
      sql: true
      format-sql: true
    packages: com.example.entity

    # Secondary DB Configuration
    secondary:
      database:
        generation: update
      packages: com.example.entity

  # OpenAPI Configuration
  swagger-ui:
    enable: true
    always-include: true
  smallrye-openapi:
    path: /openapi

  # Logging Configuration
  log:
    level: INFO
    category:
      "com.example":
        level: DEBUG

  # Cache Configuration
  cache:
    caffeine:
      "asset-cache":
        initial-capacity: 100
        maximum-size: 1000
        expire-after-write: 60M