package com.example.config;

import io.agroal.api.AgroalDataSource;
import io.quarkus.agroal.DataSource;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Configuration class for managing multiple data sources
 */
@ApplicationScoped
public class DataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceConfig.class);

    @Inject
    AgroalDataSource defaultDataSource;

    @Inject
    @DataSource("secondary")
    AgroalDataSource secondaryDataSource;

    @ConfigProperty(name = "quarkus.datasource.jdbc.url")
    String primaryDbUrl;

    @ConfigProperty(name = "quarkus.datasource.secondary.jdbc.url")
    String secondaryDbUrl;

    @Produces
    @Named("primary")
    @ApplicationScoped
    public AgroalDataSource getPrimaryDataSource() {
        logger.info("Initializing primary datasource: {}", primaryDbUrl);
        return defaultDataSource;
    }

    @Produces
    @Named("secondary")
    @ApplicationScoped
    public AgroalDataSource getSecondaryDataSource() {
        logger.info("Initializing secondary datasource: {}", secondaryDbUrl);
        return secondaryDataSource;
    }

    /**
     * Get datasource by name
     */
    public AgroalDataSource getDataSource(String name) {
        return switch (name.toLowerCase()) {
            case "primary", "default" -> defaultDataSource;
            case "secondary" -> secondaryDataSource;
            default -> throw new IllegalArgumentException("Unknown datasource: " + name);
        };
    }
}