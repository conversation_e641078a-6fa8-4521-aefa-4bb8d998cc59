package com.example.config;

import jakarta.enterprise.context.ApplicationScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Strategy for routing database operations to appropriate data sources
 */
@ApplicationScoped
public class DataSourceRoutingStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(DataSourceRoutingStrategy.class);
    
    // Thread-local storage for current datasource context
    private static final ThreadLocal<String> currentDataSource = new ThreadLocal<>();
    
    /**
     * Set the current datasource for the thread
     */
    public void setCurrentDataSource(String dataSourceName) {
        logger.debug("Setting current datasource to: {}", dataSourceName);
        currentDataSource.set(dataSourceName);
    }
    
    /**
     * Get the current datasource for the thread
     */
    public String getCurrentDataSource() {
        String current = currentDataSource.get();
        return current != null ? current : "primary";
    }
    
    /**
     * Clear the current datasource context
     */
    public void clearCurrentDataSource() {
        logger.debug("Clearing current datasource context");
        currentDataSource.remove();
    }
    
    /**
     * Route based on asset type
     */
    public String routeByAssetType(String assetType) {
        if (assetType == null) {
            return "primary";
        }
        
        // Example routing logic: route certain asset types to secondary DB
        return switch (assetType.toLowerCase()) {
            case "archive", "historical", "backup" -> "secondary";
            default -> "primary";
        };
    }
    
    /**
     * Route based on operation type
     */
    public String routeByOperation(String operation) {
        if (operation == null) {
            return "primary";
        }
        
        // Example: route read operations to secondary for load balancing
        return switch (operation.toLowerCase()) {
            case "read", "query", "search" -> "secondary";
            case "write", "update", "delete" -> "primary";
            default -> "primary";
        };
    }
    
    /**
     * Route based on tenant or user context
     */
    public String routeByTenant(String tenantId) {
        if (tenantId == null) {
            return "primary";
        }
        
        // Example: route specific tenants to different databases
        return tenantId.startsWith("tenant_") ? "secondary" : "primary";
    }
    
    /**
     * Execute with specific datasource context
     */
    public <T> T executeWithDataSource(String dataSourceName, DataSourceOperation<T> operation) {
        String previousDataSource = getCurrentDataSource();
        try {
            setCurrentDataSource(dataSourceName);
            return operation.execute();
        } finally {
            if (previousDataSource != null) {
                setCurrentDataSource(previousDataSource);
            } else {
                clearCurrentDataSource();
            }
        }
    }
    
    @FunctionalInterface
    public interface DataSourceOperation<T> {
        T execute();
    }
}
