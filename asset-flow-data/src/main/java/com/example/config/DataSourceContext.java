package com.example.config;

import com.example.annotation.DataSource;
import jakarta.enterprise.context.ApplicationScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Thread-local context for managing current datasource
 */
@ApplicationScoped
public class DataSourceContext {
    
    private static final Logger logger = LoggerFactory.getLogger(DataSourceContext.class);
    
    private static final ThreadLocal<DataSource.Type> currentDataSource = new ThreadLocal<>();
    
    /**
     * Set current datasource for the thread
     */
    public void setCurrentDataSource(DataSource.Type dataSourceType) {
        logger.debug("Setting current datasource to: {}", dataSourceType);
        currentDataSource.set(dataSourceType);
    }
    
    /**
     * Get current datasource for the thread
     */
    public DataSource.Type getCurrentDataSource() {
        DataSource.Type current = currentDataSource.get();
        if (current == null) {
            current = DataSource.Type.PRIMARY; // Default to primary
            logger.debug("No datasource set, defaulting to: {}", current);
        }
        return current;
    }
    
    /**
     * Clear current datasource context
     */
    public void clearCurrentDataSource() {
        logger.debug("Clearing datasource context");
        currentDataSource.remove();
    }
    
    /**
     * Execute operation with specific datasource
     */
    public <T> T executeWithDataSource(DataSource.Type dataSourceType, DataSourceOperation<T> operation) {
        DataSource.Type previousDataSource = currentDataSource.get();
        try {
            setCurrentDataSource(dataSourceType);
            return operation.execute();
        } finally {
            if (previousDataSource != null) {
                setCurrentDataSource(previousDataSource);
            } else {
                clearCurrentDataSource();
            }
        }
    }
    
    @FunctionalInterface
    public interface DataSourceOperation<T> {
        T execute();
    }
}
