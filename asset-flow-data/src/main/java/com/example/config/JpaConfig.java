package com.example.config;

import io.quarkus.hibernate.orm.PersistenceUnit;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.RequestScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.persistence.EntityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JPA Configuration for multiple persistence units
 */
@ApplicationScoped
public class JpaConfig {

    private static final Logger logger = LoggerFactory.getLogger(JpaConfig.class);

    @Inject
    EntityManager defaultEntityManager;

    @Inject
    @PersistenceUnit("secondary")
    EntityManager secondaryEntityManager;

    @Produces
    @Named("primary")
    @RequestScoped
    public EntityManager primaryEntityManager() {
        logger.debug("Creating primary EntityManager");
        return defaultEntityManager;
    }

    @Produces
    @Named("secondary")
    @RequestScoped
    public EntityManager secondaryEntityManager() {
        logger.debug("Creating secondary EntityManager");
        return secondaryEntityManager;
    }

    /**
     * Get EntityManager by name
     */
    public EntityManager getEntityManager(String name) {
        return switch (name.toLowerCase()) {
            case "primary", "default" -> defaultEntityManager;
            case "secondary" -> secondaryEntityManager;
            default -> throw new IllegalArgumentException("Unknown persistence unit: " + name);
        };
    }
}