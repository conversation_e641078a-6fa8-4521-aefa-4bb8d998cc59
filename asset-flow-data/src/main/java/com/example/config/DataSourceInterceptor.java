package com.example.config;

import com.example.annotation.DataSource;
import jakarta.annotation.Priority;
import jakarta.inject.Inject;
import jakarta.interceptor.AroundInvoke;
import jakarta.interceptor.Interceptor;
import jakarta.interceptor.InvocationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;

/**
 * Interceptor to handle datasource switching based on annotations
 */
@DataSource
@Interceptor
@Priority(1000)
public class DataSourceInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(DataSourceInterceptor.class);
    
    @Inject
    DataSourceContext dataSourceContext;
    
    @AroundInvoke
    public Object aroundInvoke(InvocationContext context) throws Exception {
        Method method = context.getMethod();
        Class<?> targetClass = context.getTarget().getClass();
        
        // Check method-level annotation first
        DataSource methodAnnotation = method.getAnnotation(DataSource.class);
        DataSource classAnnotation = targetClass.getAnnotation(DataSource.class);
        
        DataSource.Type dataSourceType = null;
        
        if (methodAnnotation != null) {
            dataSourceType = methodAnnotation.value();
            logger.debug("Method-level datasource annotation found: {} on {}.{}", 
                        dataSourceType, targetClass.getSimpleName(), method.getName());
        } else if (classAnnotation != null) {
            dataSourceType = classAnnotation.value();
            logger.debug("Class-level datasource annotation found: {} on {}", 
                        dataSourceType, targetClass.getSimpleName());
        }
        
        if (dataSourceType != null) {
            return dataSourceContext.executeWithDataSource(dataSourceType, () -> {
                try {
                    return context.proceed();
                } catch (Exception e) {
                    logger.error("Error executing method with datasource {}: {}", 
                               dataSourceType, e.getMessage(), e);
                    throw new RuntimeException("Database operation failed", e);
                }
            });
        } else {
            // No annotation found, proceed normally
            return context.proceed();
        }
    }
}
