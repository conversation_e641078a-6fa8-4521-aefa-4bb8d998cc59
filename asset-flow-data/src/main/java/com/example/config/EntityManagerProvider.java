package com.example.config;

import com.example.annotation.DataSource;
import io.quarkus.hibernate.orm.PersistenceUnit;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Provider for EntityManager instances based on current datasource context
 */
@ApplicationScoped
public class EntityManagerProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(EntityManagerProvider.class);
    
    @Inject
    EntityManager primaryEntityManager;
    
    @Inject
    @PersistenceUnit("secondary")
    EntityManager secondaryEntityManager;
    
    @Inject
    DataSourceContext dataSourceContext;
    
    /**
     * Get EntityManager based on current context
     */
    public EntityManager getEntityManager() {
        DataSource.Type currentDataSource = dataSourceContext.getCurrentDataSource();
        return getEntityManager(currentDataSource);
    }
    
    /**
     * Get EntityManager for specific datasource type
     */
    public EntityManager getEntityManager(DataSource.Type dataSourceType) {
        logger.debug("Getting EntityManager for datasource: {}", dataSourceType);
        
        return switch (dataSourceType) {
            case PRIMARY -> primaryEntityManager;
            case SECONDARY -> secondaryEntityManager;
        };
    }
    
    /**
     * Get primary EntityManager
     */
    public EntityManager getPrimaryEntityManager() {
        return primaryEntityManager;
    }
    
    /**
     * Get secondary EntityManager
     */
    public EntityManager getSecondaryEntityManager() {
        return secondaryEntityManager;
    }
}
