package com.example.service;

import com.example.annotation.DataSource;
import com.example.annotation.Primary;
import com.example.annotation.Secondary;
import com.example.config.DataSourceContext;
import com.example.entity.Asset;
import com.example.repository.AssetRepository;
import com.example.repository.SecondaryAssetRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service demonstrating multi-datasource usage with annotations
 */
@ApplicationScoped
public class MultiDataSourceAssetService {
    
    private static final Logger logger = LoggerFactory.getLogger(MultiDataSourceAssetService.class);
    
    @Inject
    AssetRepository primaryAssetRepository;
    
    @Inject
    SecondaryAssetRepository secondaryAssetRepository;
    
    @Inject
    DataSourceContext dataSourceContext;
    
    /**
     * Save asset to primary database
     */
    @Primary
    @Transactional
    public Asset saveToPrimary(Asset asset) {
        logger.info("Saving asset to primary database: {}", asset.getName());
        return primaryAssetRepository.save(asset);
    }
    
    /**
     * Save asset to secondary database
     */
    @Secondary
    @Transactional
    public Asset saveToSecondary(Asset asset) {
        logger.info("Saving asset to secondary database: {}", asset.getName());
        return secondaryAssetRepository.save(asset);
    }
    
    /**
     * Find asset from primary database
     */
    @Primary
    public Optional<Asset> findByIdFromPrimary(Long id) {
        logger.info("Finding asset from primary database: {}", id);
        return primaryAssetRepository.findById(id);
    }
    
    /**
     * Find asset from secondary database
     */
    @Secondary
    public Optional<Asset> findByIdFromSecondary(Long id) {
        logger.info("Finding asset from secondary database: {}", id);
        return secondaryAssetRepository.findById(id);
    }
    
    /**
     * Get all active assets from primary database
     */
    @Primary
    public List<Asset> getActiveAssetsFromPrimary() {
        logger.info("Getting active assets from primary database");
        return primaryAssetRepository.findActiveAssets();
    }
    
    /**
     * Get all archived assets from secondary database
     */
    @Secondary
    public List<Asset> getArchivedAssetsFromSecondary() {
        logger.info("Getting archived assets from secondary database");
        return secondaryAssetRepository.findArchivedAssets();
    }
    
    /**
     * Archive old assets - move from primary to secondary
     */
    @Transactional
    public void archiveOldAssets(int daysOld) {
        logger.info("Archiving assets older than {} days", daysOld);
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        
        // Find assets to archive from primary database
        List<Asset> assetsToArchive = dataSourceContext.executeWithDataSource(
            DataSource.Type.PRIMARY, 
            () -> primaryAssetRepository.findAssetsForArchival(cutoffDate)
        );
        
        logger.info("Found {} assets to archive", assetsToArchive.size());
        
        for (Asset asset : assetsToArchive) {
            // Update status to archived
            asset.setStatus("ARCHIVED");
            
            // Save to secondary database
            dataSourceContext.executeWithDataSource(
                DataSource.Type.SECONDARY,
                () -> {
                    secondaryAssetRepository.save(asset);
                    return null;
                }
            );
            
            // Remove from primary database
            dataSourceContext.executeWithDataSource(
                DataSource.Type.PRIMARY,
                () -> {
                    primaryAssetRepository.deleteById(asset.getId());
                    return null;
                }
            );
            
            logger.debug("Archived asset: {}", asset.getName());
        }
        
        logger.info("Successfully archived {} assets", assetsToArchive.size());
    }
    
    /**
     * Get statistics from both databases
     */
    public AssetStatistics getAssetStatistics() {
        logger.info("Getting asset statistics from both databases");
        
        long primaryCount = dataSourceContext.executeWithDataSource(
            DataSource.Type.PRIMARY,
            () -> primaryAssetRepository.count()
        );
        
        long secondaryCount = dataSourceContext.executeWithDataSource(
            DataSource.Type.SECONDARY,
            () -> secondaryAssetRepository.count()
        );
        
        long archivedCount = dataSourceContext.executeWithDataSource(
            DataSource.Type.SECONDARY,
            () -> secondaryAssetRepository.getTotalArchivedCount()
        );
        
        return new AssetStatistics(primaryCount, secondaryCount, archivedCount);
    }
    
    /**
     * Statistics record
     */
    public record AssetStatistics(
        long primaryCount,
        long secondaryCount,
        long archivedCount
    ) {}
}
