package com.example.repository.base;

import com.example.config.EntityManagerProvider;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Optional;

/**
 * Abstract JPA repository implementation with multi-datasource support
 */
public abstract class AbstractJpaRepository<T, ID extends Serializable> implements JpaRepository<T, ID> {
    
    private static final Logger logger = LoggerFactory.getLogger(AbstractJpaRepository.class);
    
    private final Class<T> entityClass;
    
    @Inject
    protected EntityManagerProvider entityManagerProvider;
    
    @SuppressWarnings("unchecked")
    protected AbstractJpaRepository() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
        logger.debug("Initialized repository for entity: {}", entityClass.getSimpleName());
    }
    
    /**
     * Get EntityManager based on current datasource context
     */
    protected EntityManager getEntityManager() {
        return entityManagerProvider.getEntityManager();
    }
    
    @Override
    @Transactional
    public T save(T entity) {
        logger.debug("Saving entity: {}", entity);
        EntityManager em = getEntityManager();
        em.persist(entity);
        return entity;
    }
    
    @Override
    @Transactional
    public T update(T entity) {
        logger.debug("Updating entity: {}", entity);
        EntityManager em = getEntityManager();
        return em.merge(entity);
    }
    
    @Override
    @Transactional
    public T saveOrUpdate(T entity) {
        EntityManager em = getEntityManager();
        if (em.contains(entity)) {
            return em.merge(entity);
        } else {
            em.persist(entity);
            return entity;
        }
    }
    
    @Override
    public Optional<T> findById(ID id) {
        logger.debug("Finding entity by id: {}", id);
        return Optional.ofNullable(getEntityManager().find(entityClass, id));
    }
    
    @Override
    public List<T> findAll() {
        logger.debug("Finding all entities of type: {}", entityClass.getSimpleName());
        EntityManager em = getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(entityClass);
        Root<T> rootEntry = cq.from(entityClass);
        CriteriaQuery<T> all = cq.select(rootEntry);
        TypedQuery<T> allQuery = em.createQuery(all);
        return allQuery.getResultList();
    }
    
    @Override
    public List<T> findAll(int page, int size) {
        logger.debug("Finding entities with pagination - page: {}, size: {}", page, size);
        EntityManager em = getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(entityClass);
        Root<T> rootEntry = cq.from(entityClass);
        CriteriaQuery<T> all = cq.select(rootEntry);
        TypedQuery<T> allQuery = em.createQuery(all);
        allQuery.setFirstResult(page * size);
        allQuery.setMaxResults(size);
        return allQuery.getResultList();
    }
    
    @Override
    @Transactional
    public void delete(T entity) {
        logger.debug("Deleting entity: {}", entity);
        EntityManager em = getEntityManager();
        em.remove(em.contains(entity) ? entity : em.merge(entity));
    }
    
    @Override
    @Transactional
    public void deleteById(ID id) {
        logger.debug("Deleting entity by id: {}", id);
        findById(id).ifPresent(this::delete);
    }
    
    @Override
    public boolean existsById(ID id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        logger.debug("Counting entities of type: {}", entityClass.getSimpleName());
        EntityManager em = getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        cq.select(cb.count(cq.from(entityClass)));
        return em.createQuery(cq).getSingleResult();
    }
    
    @Override
    public void flush() {
        getEntityManager().flush();
    }
    
    @Override
    public void refresh(T entity) {
        getEntityManager().refresh(entity);
    }
    
    /**
     * Get entity class
     */
    protected Class<T> getEntityClass() {
        return entityClass;
    }
}
