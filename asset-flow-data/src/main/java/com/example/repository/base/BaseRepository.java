package com.example.repository.base;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * Base repository interface for standard JPA operations
 */
public interface BaseRepository<T, ID extends Serializable> {
    
    /**
     * Save an entity
     */
    T save(T entity);
    
    /**
     * Update an entity
     */
    T update(T entity);
    
    /**
     * Find entity by ID
     */
    Optional<T> findById(ID id);
    
    /**
     * Find all entities
     */
    List<T> findAll();
    
    /**
     * Delete an entity
     */
    void delete(T entity);
    
    /**
     * Delete entity by ID
     */
    void deleteById(ID id);
    
    /**
     * Check if entity exists by ID
     */
    boolean existsById(ID id);
    
    /**
     * Count all entities
     */
    long count();
    
    /**
     * Find entities with pagination
     */
    List<T> findAll(int page, int size);
    
    /**
     * Flush changes to database
     */
    void flush();
    
    /**
     * Refresh entity from database
     */
    void refresh(T entity);
}
