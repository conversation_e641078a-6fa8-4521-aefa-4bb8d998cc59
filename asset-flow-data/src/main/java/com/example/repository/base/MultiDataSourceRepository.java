package com.example.repository.base;

import com.example.config.DataSourceRoutingStrategy;
import com.example.config.JpaConfig;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Optional;

/**
 * Abstract repository that supports multiple data sources with routing
 */
public abstract class MultiDataSourceRepository<T, ID extends Serializable> implements JpaRepository<T, ID> {
    
    private static final Logger logger = LoggerFactory.getLogger(MultiDataSourceRepository.class);
    
    private final Class<T> entityClass;
    
    @Inject
    protected JpaConfig jpaConfig;
    
    @Inject
    protected DataSourceRoutingStrategy routingStrategy;
    
    @SuppressWarnings("unchecked")
    protected MultiDataSourceRepository() {
        this.entityClass = (Class<T>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
    }
    
    /**
     * Get EntityManager based on current routing context
     */
    protected EntityManager getEntityManager() {
        String dataSourceName = routingStrategy.getCurrentDataSource();
        logger.debug("Using datasource: {} for entity: {}", dataSourceName, entityClass.getSimpleName());
        return jpaConfig.getEntityManager(dataSourceName);
    }
    
    /**
     * Get EntityManager for specific datasource
     */
    protected EntityManager getEntityManager(String dataSourceName) {
        return jpaConfig.getEntityManager(dataSourceName);
    }
    
    /**
     * Execute operation with specific datasource
     */
    protected <R> R executeWithDataSource(String dataSourceName, DataSourceOperation<R> operation) {
        return routingStrategy.executeWithDataSource(dataSourceName, () -> {
            try {
                return operation.execute(getEntityManager());
            } catch (Exception e) {
                logger.error("Error executing operation with datasource: {}", dataSourceName, e);
                throw new RuntimeException("Database operation failed", e);
            }
        });
    }
    
    @Override
    @Transactional
    public T save(T entity) {
        EntityManager em = getEntityManager();
        if (em.contains(entity)) {
            return em.merge(entity);
        } else {
            em.persist(entity);
            return entity;
        }
    }
    
    @Override
    public Optional<T> findById(ID id) {
        return Optional.ofNullable(getEntityManager().find(entityClass, id));
    }
    
    @Override
    public List<T> findAll() {
        EntityManager em = getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<T> cq = cb.createQuery(entityClass);
        Root<T> rootEntry = cq.from(entityClass);
        CriteriaQuery<T> all = cq.select(rootEntry);
        TypedQuery<T> allQuery = em.createQuery(all);
        return allQuery.getResultList();
    }
    
    @Override
    @Transactional
    public void delete(T entity) {
        EntityManager em = getEntityManager();
        em.remove(em.contains(entity) ? entity : em.merge(entity));
    }
    
    @Override
    @Transactional
    public void deleteById(ID id) {
        findById(id).ifPresent(this::delete);
    }
    
    @Override
    public boolean existsById(ID id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        EntityManager em = getEntityManager();
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        cq.select(cb.count(cq.from(entityClass)));
        return em.createQuery(cq).getSingleResult();
    }
    
    /**
     * Find all entities from specific datasource
     */
    public List<T> findAllFromDataSource(String dataSourceName) {
        return executeWithDataSource(dataSourceName, em -> {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<T> cq = cb.createQuery(entityClass);
            Root<T> rootEntry = cq.from(entityClass);
            CriteriaQuery<T> all = cq.select(rootEntry);
            TypedQuery<T> allQuery = em.createQuery(all);
            return allQuery.getResultList();
        });
    }
    
    /**
     * Count entities from specific datasource
     */
    public long countFromDataSource(String dataSourceName) {
        return executeWithDataSource(dataSourceName, em -> {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Long> cq = cb.createQuery(Long.class);
            cq.select(cb.count(cq.from(entityClass)));
            return em.createQuery(cq).getSingleResult();
        });
    }
    
    @FunctionalInterface
    protected interface DataSourceOperation<R> {
        R execute(EntityManager em) throws Exception;
    }
}
