package com.example.repository;

import com.example.annotation.Secondary;
import com.example.entity.Asset;
import com.example.repository.base.AbstractJpaRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Secondary Asset Repository implementation using standard JPA
 * Uses @Secondary annotation to route to secondary datasource
 */
@ApplicationScoped
@Secondary
public class SecondaryAssetRepository extends AbstractJpaRepository<Asset, Long> implements AssetRepositoryCustom {

    @Override
    public List<Asset> findByType(String type) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.type = :type", Asset.class);
        query.setParameter("type", type);
        return query.getResultList();
    }

    @Override
    public List<Asset> findByStatus(String status) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.status = :status", Asset.class);
        query.setParameter("status", status);
        return query.getResultList();
    }

    @Override
    public List<Asset> findByValueGreaterThan(BigDecimal value) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.value > :value", Asset.class);
        query.setParameter("value", value);
        return query.getResultList();
    }

    @Override
    public List<Asset> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.createdAt BETWEEN :start AND :end", Asset.class);
        query.setParameter("start", start);
        query.setParameter("end", end);
        return query.getResultList();
    }

    @Override
    public List<Asset> findByNameLike(String namePattern) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.name LIKE :pattern", Asset.class);
        query.setParameter("pattern", "%" + namePattern + "%");
        return query.getResultList();
    }

    @Override
    public long countByType(String type) {
        EntityManager em = getEntityManager();
        TypedQuery<Long> query = em.createQuery(
            "SELECT COUNT(a) FROM Asset a WHERE a.type = :type", Long.class);
        query.setParameter("type", type);
        return query.getSingleResult();
    }

    @Override
    public BigDecimal sumValueByType(String type) {
        EntityManager em = getEntityManager();
        TypedQuery<BigDecimal> query = em.createQuery(
            "SELECT SUM(a.value) FROM Asset a WHERE a.type = :type", BigDecimal.class);
        query.setParameter("type", type);
        BigDecimal result = query.getSingleResult();
        return result != null ? result : BigDecimal.ZERO;
    }

    @Override
    public List<Asset> findActiveAssets() {
        return findByStatus("ACTIVE");
    }

    @Override
    public List<Asset> findByTypeAndStatus(String type, String status) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.type = :type AND a.status = :status", Asset.class);
        query.setParameter("type", type);
        query.setParameter("status", status);
        return query.getResultList();
    }

    @Override
    public List<Asset> findRecentAssets(int days) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.createdAt >= :cutoffDate", Asset.class);
        query.setParameter("cutoffDate", cutoffDate);
        return query.getResultList();
    }

    @Override
    public List<Asset> findTopAssetsByValue(int limit) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a ORDER BY a.value DESC", Asset.class);
        query.setMaxResults(limit);
        return query.getResultList();
    }

    @Override
    public List<Asset> findAssetsForArchival(LocalDateTime beforeDate) {
        EntityManager em = getEntityManager();
        TypedQuery<Asset> query = em.createQuery(
            "SELECT a FROM Asset a WHERE a.createdAt < :beforeDate AND a.status != 'ARCHIVED'", Asset.class);
        query.setParameter("beforeDate", beforeDate);
        return query.getResultList();
    }

    /**
     * Secondary-specific methods for archived data
     */
    public List<Asset> findArchivedAssets() {
        return findByStatus("ARCHIVED");
    }

    public long getTotalArchivedCount() {
        EntityManager em = getEntityManager();
        TypedQuery<Long> query = em.createQuery(
            "SELECT COUNT(a) FROM Asset a WHERE a.status = 'ARCHIVED'", Long.class);
        return query.getSingleResult();
    }

    public BigDecimal getTotalArchivedValue() {
        EntityManager em = getEntityManager();
        TypedQuery<BigDecimal> query = em.createQuery(
            "SELECT SUM(a.value) FROM Asset a WHERE a.status = 'ARCHIVED'", BigDecimal.class);
        BigDecimal result = query.getSingleResult();
        return result != null ? result : BigDecimal.ZERO;
    }
}
