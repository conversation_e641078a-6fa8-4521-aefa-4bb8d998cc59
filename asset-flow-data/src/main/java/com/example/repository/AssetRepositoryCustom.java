package com.example.repository;

import com.example.entity.Asset;
import com.example.repository.base.JpaRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Custom Asset Repository interface with business-specific methods
 */
public interface AssetRepositoryCustom extends JpaRepository<Asset, Long> {
    
    /**
     * Find assets by type
     */
    List<Asset> findByType(String type);
    
    /**
     * Find assets by status
     */
    List<Asset> findByStatus(String status);
    
    /**
     * Find assets with value greater than specified amount
     */
    List<Asset> findByValueGreaterThan(BigDecimal value);
    
    /**
     * Find assets created between dates
     */
    List<Asset> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
    
    /**
     * Find assets by name pattern
     */
    List<Asset> findByNameLike(String namePattern);
    
    /**
     * Count assets by type
     */
    long countByType(String type);
    
    /**
     * Sum value by type
     */
    BigDecimal sumValueByType(String type);
    
    /**
     * Find active assets
     */
    List<Asset> findActiveAssets();
    
    /**
     * Find assets by type and status
     */
    List<Asset> findByTypeAndStatus(String type, String status);
    
    /**
     * Find recent assets (created in last N days)
     */
    List<Asset> findRecentAssets(int days);
    
    /**
     * Find top N assets by value
     */
    List<Asset> findTopAssetsByValue(int limit);
    
    /**
     * Find assets for archival (older than specified date)
     */
    List<Asset> findAssetsForArchival(LocalDateTime beforeDate);
}
