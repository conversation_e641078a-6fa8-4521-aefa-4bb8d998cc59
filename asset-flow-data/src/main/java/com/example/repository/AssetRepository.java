package com.example.repository;

import com.example.entity.Asset;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import jakarta.enterprise.context.ApplicationScoped;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Primary Asset Repository using Panache for PostgreSQL
 */
@ApplicationScoped
public class AssetRepository implements PanacheRepository<Asset> {

    /**
     * Find assets by type
     */
    public List<Asset> findByType(String type) {
        return find("type", type).list();
    }

    /**
     * Find assets by status
     */
    public List<Asset> findByStatus(String status) {
        return find("status", status).list();
    }

    /**
     * Find assets with value greater than specified amount
     */
    public List<Asset> findByValueGreaterThan(BigDecimal value) {
        return find("value > ?1", value).list();
    }

    /**
     * Find assets created between dates
     */
    public List<Asset> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end) {
        return find("createdAt between ?1 and ?2", start, end).list();
    }

    /**
     * Find assets by name pattern
     */
    public List<Asset> findByNameLike(String namePattern) {
        return find("name like ?1", "%" + namePattern + "%").list();
    }

    /**
     * Count assets by type
     */
    public long countByType(String type) {
        return count("type", type);
    }

    /**
     * Sum value by type
     */
    public BigDecimal sumValueByType(String type) {
        return find("select sum(a.value) from Asset a where a.type = ?1", type)
                .project(BigDecimal.class)
                .firstResult();
    }

    /**
     * Find active assets
     */
    public List<Asset> findActiveAssets() {
        return find("status = 'ACTIVE'").list();
    }

    /**
     * Find assets by type and status
     */
    public List<Asset> findByTypeAndStatus(String type, String status) {
        return find("type = ?1 and status = ?2", type, status).list();
    }

    /**
     * Find recent assets (created in last N days)
     */
    public List<Asset> findRecentAssets(int days) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        return find("createdAt >= ?1", cutoffDate).list();
    }

    /**
     * Find top N assets by value
     */
    public List<Asset> findTopAssetsByValue(int limit) {
        return find("order by value desc").page(0, limit).list();
    }
}
