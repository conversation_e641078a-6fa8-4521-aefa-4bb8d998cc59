package com.example.repository;

import com.example.annotation.Primary;
import com.example.entity.Asset;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 主数据源 Asset Repository - 超简化版本
 */
@ApplicationScoped
@Primary
public class AssetRepository {
    
    @Inject
    EntityManager em;  // 默认使用主数据源
    
    @Transactional
    public Asset save(Asset asset) {
        if (asset.getId() == null) {
            em.persist(asset);
        } else {
            asset = em.merge(asset);
        }
        return asset;
    }
    
    public Optional<Asset> findById(Long id) {
        return Optional.ofNullable(em.find(Asset.class, id));
    }
    
    public List<Asset> findAll() {
        return em.createQuery("SELECT a FROM Asset a", Asset.class).getResultList();
    }
    
    public List<Asset> findByType(String type) {
        return em.createQuery("SELECT a FROM Asset a WHERE a.type = :type", Asset.class)
                .setParameter("type", type)
                .getResultList();
    }
    
    public List<Asset> findByStatus(String status) {
        return em.createQuery("SELECT a FROM Asset a WHERE a.status = :status", Asset.class)
                .setParameter("status", status)
                .getResultList();
    }
    
    public List<Asset> findActiveAssets() {
        return findByStatus("ACTIVE");
    }
    
    public long count() {
        return em.createQuery("SELECT COUNT(a) FROM Asset a", Long.class).getSingleResult();
    }
    
    @Transactional
    public void deleteById(Long id) {
        findById(id).ifPresent(asset -> em.remove(asset));
    }
    
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }
}
